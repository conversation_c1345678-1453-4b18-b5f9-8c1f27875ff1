package tn.esprit.reclamation.Services;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tn.esprit.reclamation.Entities.ReponseReclamation;
import tn.esprit.reclamation.Entities.Reclamation;
import tn.esprit.reclamation.Entities.User;
import tn.esprit.reclamation.Repository.ReponseReclamationRepository;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class ReponseReclamationService {
    
    @Autowired
    private ReponseReclamationRepository reponseRepository;

    @Autowired
    private NotificationService notificationService;

    public List<ReponseReclamation> getAllReponses() {
        return reponseRepository.findAll();
    }

    public Optional<ReponseReclamation> getReponseById(Long id) {
        return reponseRepository.findById(id);
    }

    public ReponseReclamation saveReponse(ReponseReclamation reponse) {
        return reponseRepository.save(reponse);
    }

    public void deleteReponse(Long id) {
        reponseRepository.deleteById(id);
    }

    // Méthodes spécifiques
    public List<ReponseReclamation> getReponsesByReclamation(Reclamation reclamation) {
        return reponseRepository.findByReclamationOrderByDateReponseDesc(reclamation);
    }

    public long countReponsesByReclamation(Reclamation reclamation) {
        return reponseRepository.countByReclamation(reclamation);
    }

    public ReponseReclamation createReponseForReclamation(String message, Reclamation reclamation, User agent) {
        ReponseReclamation reponse = new ReponseReclamation(message, reclamation, agent);
        ReponseReclamation savedReponse = saveReponse(reponse);

        // Créer une notification pour le client
        notificationService.createReponseNotification(reclamation.getUser(), reclamation, agent.getNom());

        return savedReponse;
    }
}
