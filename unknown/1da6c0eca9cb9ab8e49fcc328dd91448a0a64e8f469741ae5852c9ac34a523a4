package tn.esprit.reclamation.Repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import tn.esprit.reclamation.Entities.Notification;
import tn.esprit.reclamation.Entities.User;
import java.util.List;

@Repository
public interface NotificationRepository extends JpaRepository<Notification, Long> {
    
    // Trouver toutes les notifications d'un utilisateur (les plus récentes en premier)
    List<Notification> findByUserOrderByDateCreationDesc(User user);
    
    // Trouver les notifications non lues d'un utilisateur
    List<Notification> findByUserAndLuFalseOrderByDateCreationDesc(User user);
    
    // Compter les notifications non lues d'un utilisateur
    long countByUserAndLuFalse(User user);
    
    // Trouver les notifications par type pour un utilisateur
    List<Notification> findByUserAndTypeOrderByDateCreationDesc(User user, String type);
}
