import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';

export interface ReclamationNotification {
  id?: number;
  titre: string;
  message: string;
  type: string;
  lu: boolean;
  dateCreation: string;
  reclamation?: {
    id: number;
    titre: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class ReclamationNotificationService {
  private apiUrl = 'http://localhost:8081/api/notifications';
  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json'
    })
  };

  // Subject pour le compteur de notifications non lues
  private unreadCountSubject = new BehaviorSubject<number>(0);
  public unreadCount$ = this.unreadCountSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Récupérer toutes les notifications d'un utilisateur
  getNotificationsByUser(userId: number): Observable<ReclamationNotification[]> {
    return this.http.get<ReclamationNotification[]>(`${this.apiUrl}/user/${userId}`);
  }

  // Récupérer les notifications non lues d'un utilisateur
  getUnreadNotificationsByUser(userId: number): Observable<ReclamationNotification[]> {
    return this.http.get<ReclamationNotification[]>(`${this.apiUrl}/user/${userId}/unread`);
  }

  // Compter les notifications non lues d'un utilisateur
  getUnreadNotificationsCount(userId: number): Observable<{count: number}> {
    return this.http.get<{count: number}>(`${this.apiUrl}/user/${userId}/unread/count`).pipe(
      tap(response => this.unreadCountSubject.next(response.count))
    );
  }

  // Marquer une notification comme lue
  markAsRead(notificationId: number): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/${notificationId}/read`, {}, this.httpOptions);
  }

  // Marquer toutes les notifications comme lues pour un utilisateur
  markAllAsReadForUser(userId: number): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/user/${userId}/read-all`, {}, this.httpOptions).pipe(
      tap(() => this.unreadCountSubject.next(0))
    );
  }

  // Mettre à jour le compteur de notifications non lues
  updateUnreadCount(userId: number): void {
    this.getUnreadNotificationsCount(userId).subscribe();
  }

  // Obtenir l'icône selon le type de notification
  getNotificationIcon(type: string): string {
    const icons: { [key: string]: string } = {
      'REPONSE': '💬',
      'STATUT_CHANGE': '🔄',
      'NOUVELLE_RECLAMATION': '📋'
    };
    return icons[type] || '🔔';
  }

  // Obtenir la couleur selon le type de notification
  getNotificationColor(type: string): string {
    const colors: { [key: string]: string } = {
      'REPONSE': '#28a745',
      'STATUT_CHANGE': '#007bff',
      'NOUVELLE_RECLAMATION': '#ffc107'
    };
    return colors[type] || '#6c757d';
  }

  // Formater la date
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'À l\'instant';
    } else if (diffInMinutes < 60) {
      return `Il y a ${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''}`;
    } else if (diffInMinutes < 1440) { // 24 heures
      const hours = Math.floor(diffInMinutes / 60);
      return `Il y a ${hours} heure${hours > 1 ? 's' : ''}`;
    } else {
      return date.toLocaleDateString('fr-FR', {
        day: 'numeric',
        month: 'short',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  }
}
