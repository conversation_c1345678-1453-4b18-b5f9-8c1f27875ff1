import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { ReclamationService, Reclamation, ReclamationStats } from '../../services/reclamation.service';
import { User } from '../../services/user.service';

@Component({
  selector: 'app-reclamations',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterLink],
  templateUrl: './reclamations.component.html',
  styleUrls: ['./reclamations.component.scss']
})
export class ReclamationsComponent implements OnInit {
  currentUser: User | null = null;
  reclamations: Reclamation[] = [];
  stats: ReclamationStats | null = null;
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  
  // Formulaire de nouvelle réclamation
  showNewReclamationForm = false;
  newReclamation: Reclamation = {
    titre: '',
    description: '',
    type: 'PROBLEME_RESEAU'
  };

  types = [
    { value: 'PROBLEME_RESEAU', label: 'Problème Réseau' },
    { value: 'FACTURATION', label: 'Facturation' },
    { value: 'ABONNEMENT', label: 'Abonnement' },
    { value: 'SERVICE_CLIENT', label: 'Service Client' },
    { value: 'AUTRE', label: 'Autre' }
  ];

  constructor(
    private authService: AuthService,
    private reclamationService: ReclamationService,
    private router: Router
  ) {}

  ngOnInit() {
    this.currentUser = this.authService.currentUserValue;
    
    if (!this.currentUser) {
      this.router.navigate(['/auth']);
      return;
    }

    this.loadReclamations();
    this.loadStats();
  }

  loadReclamations() {
    if (!this.currentUser?.id) return;
    
    this.isLoading = true;
    this.reclamationService.getReclamationsByUser(this.currentUser.id).subscribe({
      next: (reclamations) => {
        this.reclamations = reclamations;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des réclamations:', error);
        this.errorMessage = 'Erreur lors du chargement des réclamations';
        this.isLoading = false;
      }
    });
  }

  loadStats() {
    if (!this.currentUser?.id) return;
    
    this.reclamationService.getUserReclamationStats(this.currentUser.id).subscribe({
      next: (stats) => {
        this.stats = stats;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des statistiques:', error);
      }
    });
  }

  toggleNewReclamationForm() {
    this.showNewReclamationForm = !this.showNewReclamationForm;
    if (!this.showNewReclamationForm) {
      this.resetForm();
    }
  }

  onSubmitReclamation() {
    if (!this.currentUser?.id) return;
    
    if (!this.isFormValid()) {
      this.errorMessage = 'Veuillez remplir tous les champs obligatoires';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.reclamationService.createReclamationForUser(this.currentUser.id, this.newReclamation).subscribe({
      next: (reclamation) => {
        this.successMessage = 'Réclamation créée avec succès !';
        this.resetForm();
        this.showNewReclamationForm = false;
        this.loadReclamations();
        this.loadStats();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erreur lors de la création de la réclamation:', error);
        this.errorMessage = 'Erreur lors de la création de la réclamation';
        this.isLoading = false;
      }
    });
  }

  private isFormValid(): boolean {
    return !!(
      this.newReclamation.titre.trim() &&
      this.newReclamation.description.trim() &&
      this.newReclamation.type
    );
  }

  private resetForm() {
    this.newReclamation = {
      titre: '',
      description: '',
      type: 'PROBLEME_RESEAU'
    };
    this.errorMessage = '';
    this.successMessage = '';
  }

  getTypeDisplayName(type: string): string {
    return this.reclamationService.getTypeDisplayName(type);
  }

  getStatutDisplayName(statut: string): string {
    return this.reclamationService.getStatutDisplayName(statut);
  }

  getStatutColor(statut: string): string {
    return this.reclamationService.getStatutColor(statut);
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  logout() {
    this.authService.logout().subscribe({
      next: () => {
        this.router.navigate(['/']);
      },
      error: (error) => {
        console.error('Erreur lors de la déconnexion:', error);
        this.router.navigate(['/']);
      }
    });
  }
}
