package tn.esprit.reclamation.Services;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tn.esprit.reclamation.Entities.Reclamation;
import tn.esprit.reclamation.Entities.StatutReclamation;
import tn.esprit.reclamation.Entities.User;
import tn.esprit.reclamation.Repository.ReclamationRepository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class ReclamationService {
    @Autowired
    private ReclamationRepository reclamationRepository;

    @Autowired
    private NotificationService notificationService;

    public List<Reclamation> getAllReclamations() {
        return reclamationRepository.findAll();
    }

    public Optional<Reclamation> getReclamationById(Long id) {
        return reclamationRepository.findById(id);
    }

    public Reclamation saveReclamation(Reclamation reclamation) {
        // Les dates et le statut sont gérés automatiquement par @PrePersist et @PreUpdate
        return reclamationRepository.save(reclamation);
    }

    public Reclamation updateStatutWithNotification(Reclamation reclamation, StatutReclamation nouveauStatut) {
        StatutReclamation ancienStatut = reclamation.getStatut();
        reclamation.setStatut(nouveauStatut);
        Reclamation updatedReclamation = saveReclamation(reclamation);

        // Créer une notification si le statut a changé
        if (ancienStatut != nouveauStatut) {
            notificationService.createStatutChangeNotification(
                reclamation.getUser(),
                reclamation,
                ancienStatut.toString(),
                nouveauStatut.toString()
            );
        }

        return updatedReclamation;
    }

    public void deleteReclamation(Long id) {
        reclamationRepository.deleteById(id);
    }

    // Méthodes spécifiques aux utilisateurs
    public List<Reclamation> getReclamationsByUser(User user) {
        return reclamationRepository.findByUserOrderByDateCreationDesc(user);
    }

    public List<Reclamation> getReclamationsByUserAndStatut(User user, StatutReclamation statut) {
        return reclamationRepository.findByUserAndStatutOrderByDateCreationDesc(user, statut);
    }

    public long countReclamationsByUser(User user) {
        return reclamationRepository.countByUser(user);
    }

    public Reclamation createReclamationForUser(Reclamation reclamation, User user) {
        reclamation.setUser(user);
        return saveReclamation(reclamation);
    }
}
