package tn.esprit.reclamation.Controllers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tn.esprit.reclamation.Entities.Reclamation;
import tn.esprit.reclamation.Entities.StatutReclamation;
import tn.esprit.reclamation.Entities.User;
import tn.esprit.reclamation.Services.ReclamationService;
import tn.esprit.reclamation.Services.UserService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/reclamations")
@CrossOrigin(origins = "http://localhost:4200")
public class ReclamationController {
    @Autowired
    private ReclamationService reclamationService;

    @Autowired
    private UserService userService;

    @GetMapping
    public List<Reclamation> getAllReclamations() {
        return reclamationService.getAllReclamations();
    }

    @GetMapping("/{id}")
    public ResponseEntity<Reclamation> getReclamationById(@PathVariable Long id) {
        Optional<Reclamation> reclamation = reclamationService.getReclamationById(id);
        return reclamation.map(ResponseEntity::ok).orElseGet(() -> ResponseEntity.notFound().build());
    }

    @PostMapping
    public Reclamation createReclamation(@RequestBody Reclamation reclamation) {
        return reclamationService.saveReclamation(reclamation);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteReclamation(@PathVariable Long id) {
        reclamationService.deleteReclamation(id);
        return ResponseEntity.noContent().build();
    }

    // Endpoints spécifiques aux utilisateurs
    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getReclamationsByUser(@PathVariable Long userId) {
        try {
            User user = userService.getUserById(userId);
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            List<Reclamation> reclamations = reclamationService.getReclamationsByUser(user);

            // Retourner directement la liste des réclamations
            return ResponseEntity.ok(reclamations);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de la récupération des réclamations");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    @PostMapping("/user/{userId}")
    public ResponseEntity<?> createReclamationForUser(@PathVariable Long userId, @RequestBody Reclamation reclamation) {
        try {
            User user = userService.getUserById(userId);
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            Reclamation createdReclamation = reclamationService.createReclamationForUser(reclamation, user);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdReclamation);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de la création de la réclamation: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    @GetMapping("/user/{userId}/stats")
    public ResponseEntity<?> getUserReclamationStats(@PathVariable Long userId) {
        try {
            User user = userService.getUserById(userId);
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            Map<String, Object> stats = new HashMap<>();
            stats.put("total", reclamationService.countReclamationsByUser(user));
            stats.put("nouvelles", reclamationService.getReclamationsByUserAndStatut(user, StatutReclamation.NOUVELLE).size());
            stats.put("enCours", reclamationService.getReclamationsByUserAndStatut(user, StatutReclamation.EN_COURS).size());
            stats.put("resolues", reclamationService.getReclamationsByUserAndStatut(user, StatutReclamation.RESOLUE).size());

            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de la récupération des statistiques");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
}
