package tn.esprit.reclamation.Controllers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tn.esprit.reclamation.Entities.Reclamation;
import tn.esprit.reclamation.Entities.ReponseReclamation;
import tn.esprit.reclamation.Entities.StatutReclamation;
import tn.esprit.reclamation.Entities.User;
import tn.esprit.reclamation.Services.ReclamationService;
import tn.esprit.reclamation.Services.ReponseReclamationService;
import tn.esprit.reclamation.Services.UserService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/reclamations")
@CrossOrigin(origins = "http://localhost:4200")
public class ReclamationController {
    @Autowired
    private ReclamationService reclamationService;

    @Autowired
    private UserService userService;

    @Autowired
    private ReponseReclamationService reponseService;

    @GetMapping
    public List<Reclamation> getAllReclamations() {
        return reclamationService.getAllReclamations();
    }

    @GetMapping("/{id}")
    public ResponseEntity<Reclamation> getReclamationById(@PathVariable Long id) {
        Optional<Reclamation> reclamation = reclamationService.getReclamationById(id);
        return reclamation.map(ResponseEntity::ok).orElseGet(() -> ResponseEntity.notFound().build());
    }

    @PostMapping
    public Reclamation createReclamation(@RequestBody Reclamation reclamation) {
        return reclamationService.saveReclamation(reclamation);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteReclamation(@PathVariable Long id) {
        reclamationService.deleteReclamation(id);
        return ResponseEntity.noContent().build();
    }

    // Endpoints spécifiques aux utilisateurs
    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getReclamationsByUser(@PathVariable Long userId) {
        try {
            User user = userService.getUserById(userId);
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            List<Reclamation> reclamations = reclamationService.getReclamationsByUser(user);

            // Retourner directement la liste des réclamations
            return ResponseEntity.ok(reclamations);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de la récupération des réclamations");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    @PostMapping("/user/{userId}")
    public ResponseEntity<?> createReclamationForUser(@PathVariable Long userId, @RequestBody Reclamation reclamation) {
        try {
            User user = userService.getUserById(userId);
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            Reclamation createdReclamation = reclamationService.createReclamationForUser(reclamation, user);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdReclamation);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de la création de la réclamation: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    @GetMapping("/user/{userId}/stats")
    public ResponseEntity<?> getUserReclamationStats(@PathVariable Long userId) {
        try {
            User user = userService.getUserById(userId);
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            Map<String, Object> stats = new HashMap<>();
            stats.put("total", reclamationService.countReclamationsByUser(user));
            stats.put("nouvelles", reclamationService.getReclamationsByUserAndStatut(user, StatutReclamation.NOUVELLE).size());
            stats.put("enCours", reclamationService.getReclamationsByUserAndStatut(user, StatutReclamation.EN_COURS).size());
            stats.put("resolues", reclamationService.getReclamationsByUserAndStatut(user, StatutReclamation.RESOLUE).size());

            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de la récupération des statistiques");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    // ========== ENDPOINTS POUR LES AGENTS ==========

    // Mettre à jour le statut d'une réclamation
    @PutMapping("/{id}/statut")
    public ResponseEntity<?> updateReclamationStatut(@PathVariable Long id, @RequestBody Map<String, String> request) {
        try {
            Optional<Reclamation> optionalReclamation = reclamationService.getReclamationById(id);
            if (optionalReclamation.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            Reclamation reclamation = optionalReclamation.get();
            String nouveauStatut = request.get("statut");

            try {
                StatutReclamation statut = StatutReclamation.valueOf(nouveauStatut);
                reclamation.setStatut(statut);
                Reclamation updatedReclamation = reclamationService.saveReclamation(reclamation);
                return ResponseEntity.ok(updatedReclamation);
            } catch (IllegalArgumentException e) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "Statut invalide: " + nouveauStatut);
                return ResponseEntity.badRequest().body(errorResponse);
            }
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de la mise à jour du statut: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    // Ajouter une réponse à une réclamation
    @PostMapping("/{id}/reponse")
    public ResponseEntity<?> addReponseToReclamation(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        try {
            Optional<Reclamation> optionalReclamation = reclamationService.getReclamationById(id);
            if (optionalReclamation.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            Reclamation reclamation = optionalReclamation.get();
            String message = (String) request.get("message");
            Long agentId = Long.valueOf(request.get("agentId").toString());

            User agent = userService.getUserById(agentId);
            if (agent == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "Agent non trouvé");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            ReponseReclamation reponse = reponseService.createReponseForReclamation(message, reclamation, agent);
            return ResponseEntity.status(HttpStatus.CREATED).body(reponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de l'ajout de la réponse: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    // Récupérer les réponses d'une réclamation
    @GetMapping("/{id}/reponses")
    public ResponseEntity<?> getReponsesByReclamation(@PathVariable Long id) {
        try {
            Optional<Reclamation> optionalReclamation = reclamationService.getReclamationById(id);
            if (optionalReclamation.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            Reclamation reclamation = optionalReclamation.get();
            List<ReponseReclamation> reponses = reponseService.getReponsesByReclamation(reclamation);
            return ResponseEntity.ok(reponses);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de la récupération des réponses: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
}
