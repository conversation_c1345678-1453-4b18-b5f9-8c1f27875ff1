import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink, Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { User } from '../../services/user.service';
import { ReclamationNotificationService, ReclamationNotification } from '../../services/reclamation-notification.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  currentUser: User | null = null;
  unreadNotificationsCount = 0;
  showNotifications = false;
  notifications: ReclamationNotification[] = [];

  constructor(
    private authService: AuthService,
    private router: Router,
    private notificationService: ReclamationNotificationService
  ) {}

  ngOnInit() {
    // Vérifier si l'utilisateur est connecté
    this.currentUser = this.authService.currentUserValue;

    if (!this.currentUser) {
      // Rediriger vers la page de connexion si pas connecté
      this.router.navigate(['/auth']);
      return;
    }

    // Charger le compteur de notifications
    this.loadUnreadNotificationsCount();

    // S'abonner aux changements du compteur
    this.notificationService.unreadCount$.subscribe(count => {
      this.unreadNotificationsCount = count;
    });
  }

  logout() {
    this.authService.logout().subscribe({
      next: () => {
        this.router.navigate(['/']);
      },
      error: (error) => {
        console.error('Erreur lors de la déconnexion:', error);
        // Même en cas d'erreur, on déconnecte localement
        this.router.navigate(['/']);
      }
    });
  }

  getRoleDisplayName(role: string): string {
    switch (role) {
      case 'ADMIN': return 'Administrateur';
      case 'AGENT': return 'Agent de support';
      case 'CLIENT': return 'Client';
      default: return role;
    }
  }

  getRoleColor(role: string): string {
    switch (role) {
      case 'ADMIN': return '#dc3545';
      case 'AGENT': return '#fd7e14';
      case 'CLIENT': return '#198754';
      default: return '#6c757d';
    }
  }

  onImageError(event: any) {
    // En cas d'erreur de chargement de l'image, utiliser une image par défaut
    event.target.src = 'assets/img/default-avatar.svg';
  }

  // Méthodes pour les notifications
  loadUnreadNotificationsCount() {
    if (this.currentUser?.id) {
      this.notificationService.updateUnreadCount(this.currentUser.id);
    }
  }

  toggleNotifications() {
    this.showNotifications = !this.showNotifications;
    if (this.showNotifications && this.currentUser?.id) {
      this.loadNotifications();
    }
  }

  loadNotifications() {
    if (this.currentUser?.id) {
      this.notificationService.getNotificationsByUser(this.currentUser.id).subscribe({
        next: (notifications) => {
          this.notifications = notifications;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des notifications:', error);
        }
      });
    }
  }

  markAsRead(notification: ReclamationNotification) {
    if (notification.id && !notification.lu) {
      this.notificationService.markAsRead(notification.id).subscribe({
        next: () => {
          notification.lu = true;
          this.loadUnreadNotificationsCount();
        },
        error: (error) => {
          console.error('Erreur lors du marquage comme lu:', error);
        }
      });
    }
  }

  markAllAsRead() {
    if (this.currentUser?.id) {
      this.notificationService.markAllAsReadForUser(this.currentUser.id).subscribe({
        next: () => {
          this.notifications.forEach(n => n.lu = true);
          this.unreadNotificationsCount = 0;
        },
        error: (error) => {
          console.error('Erreur lors du marquage de toutes comme lues:', error);
        }
      });
    }
  }

  getNotificationIcon(type: string): string {
    return this.notificationService.getNotificationIcon(type);
  }

  getNotificationColor(type: string): string {
    return this.notificationService.getNotificationColor(type);
  }

  formatNotificationDate(dateString: string): string {
    return this.notificationService.formatDate(dateString);
  }
}
