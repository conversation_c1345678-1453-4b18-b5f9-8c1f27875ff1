import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink, Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { User } from '../../services/user.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  currentUser: User | null = null;

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit() {
    // Vérifier si l'utilisateur est connecté
    this.currentUser = this.authService.currentUserValue;
    
    if (!this.currentUser) {
      // Rediriger vers la page de connexion si pas connecté
      this.router.navigate(['/auth']);
    }
  }

  logout() {
    this.authService.logout().subscribe({
      next: () => {
        this.router.navigate(['/']);
      },
      error: (error) => {
        console.error('Erreur lors de la déconnexion:', error);
        // Même en cas d'erreur, on déconnecte localement
        this.router.navigate(['/']);
      }
    });
  }

  getRoleDisplayName(role: string): string {
    switch (role) {
      case 'ADMIN': return 'Administrateur';
      case 'AGENT': return 'Agent de support';
      case 'CLIENT': return 'Client';
      default: return role;
    }
  }

  getRoleColor(role: string): string {
    switch (role) {
      case 'ADMIN': return '#dc3545';
      case 'AGENT': return '#fd7e14';
      case 'CLIENT': return '#198754';
      default: return '#6c757d';
    }
  }

  onImageError(event: any) {
    // En cas d'erreur de chargement de l'image, utiliser une image par défaut
    event.target.src = 'assets/img/default-avatar.svg';
  }
}
