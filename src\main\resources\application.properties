# Database config
spring.datasource.url=***********************************************
spring.datasource.username=postgres
spring.datasource.password=1234

# JPA config
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
# spring.jpa.properties.hibernate.default_schema=reclamation_db

# Configuration pour la persistance des données
# spring.sql.init.mode=never
# spring.sql.init.platform=postgres

# Server port (optionnel)
server.port=8081

# Debug mode pour diagnostiquer les erreurs
debug=true

# Configuration DevTools pour éviter les redémarrages automatiques
spring.devtools.restart.enabled=false
spring.devtools.livereload.enabled=false

# OAuth2 Configuration
spring.security.oauth2.client.registration.google.client-id=YOUR_GOOGLE_CLIENT_ID
spring.security.oauth2.client.registration.google.client-secret=YOUR_GOOGLE_CLIENT_SECRET
spring.security.oauth2.client.registration.google.scope=profile,email
spring.security.oauth2.client.registration.google.redirect-uri=http://localhost:8081/oauth2/callback/google

spring.security.oauth2.client.registration.facebook.client-id=YOUR_FACEBOOK_CLIENT_ID
spring.security.oauth2.client.registration.facebook.client-secret=YOUR_FACEBOOK_CLIENT_SECRET
spring.security.oauth2.client.registration.facebook.scope=email,public_profile
spring.security.oauth2.client.registration.facebook.redirect-uri=http://localhost:8081/oauth2/callback/facebook

spring.security.oauth2.client.registration.github.client-id=YOUR_GITHUB_CLIENT_ID
spring.security.oauth2.client.registration.github.client-secret=YOUR_GITHUB_CLIENT_SECRET
spring.security.oauth2.client.registration.github.scope=user:email
spring.security.oauth2.client.registration.github.redirect-uri=http://localhost:8081/oauth2/callback/github

# JWT Configuration
app.jwtSecret=mySecretKey
app.jwtExpirationInMs=604800000