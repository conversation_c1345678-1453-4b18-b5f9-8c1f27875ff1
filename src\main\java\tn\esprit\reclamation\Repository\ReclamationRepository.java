package tn.esprit.reclamation.Repository;

import org.springframework.data.jpa.repository.JpaRepository;
import tn.esprit.reclamation.Entities.Reclamation;
import tn.esprit.reclamation.Entities.User;
import java.util.List;

public interface ReclamationRepository extends JpaRepository<Reclamation, Long> {

    // Trouver toutes les réclamations d'un utilisateur
    List<Reclamation> findByUserOrderByDateCreationDesc(User user);

    // Trouver les réclamations d'un utilisateur par statut
    List<Reclamation> findByUserAndStatutOrderByDateCreationDesc(User user, tn.esprit.reclamation.Entities.StatutReclamation statut);

    // Compter les réclamations d'un utilisateur
    long countByUser(User user);
}
