package tn.esprit.reclamation.Repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import tn.esprit.reclamation.Entities.ReponseReclamation;
import tn.esprit.reclamation.Entities.Reclamation;
import java.util.List;

@Repository
public interface ReponseReclamationRepository extends JpaRepository<ReponseReclamation, Long> {
    
    // Trouver toutes les réponses d'une réclamation
    List<ReponseReclamation> findByReclamationOrderByDateReponseDesc(Reclamation reclamation);
    
    // Compter les réponses d'une réclamation
    long countByReclamation(Reclamation reclamation);
}
