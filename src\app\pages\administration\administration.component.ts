import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { ReclamationService, Reclamation } from '../../services/reclamation.service';
import { UserService, User } from '../../services/user.service';

@Component({
  selector: 'app-administration',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterLink],
  templateUrl: './administration.component.html',
  styleUrls: ['./administration.component.scss']
})
export class AdministrationComponent implements OnInit {
  currentUser: User | null = null;
  allReclamations: Reclamation[] = [];
  allUsers: User[] = [];
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  
  // Filtres
  selectedStatut = '';
  selectedType = '';
  searchTerm = '';
  
  // Réponse à une réclamation
  selectedReclamation: Reclamation | null = null;
  reponseMessage = '';
  showReponseModal = false;
  
  // Changement de statut
  showStatutModal = false;
  nouveauStatut = '';

  statuts = [
    { value: 'EN_ATTENTE', label: 'En attente' },
    { value: 'NOUVELLE', label: 'Nouvelle' },
    { value: 'EN_COURS', label: 'En cours' },
    { value: 'RESOLUE', label: 'Résolue' }
  ];

  types = [
    { value: 'PROBLEME_RESEAU', label: 'Problème Réseau' },
    { value: 'FACTURATION', label: 'Facturation' },
    { value: 'ABONNEMENT', label: 'Abonnement' },
    { value: 'SERVICE_CLIENT', label: 'Service Client' },
    { value: 'AUTRE', label: 'Autre' }
  ];

  constructor(
    private authService: AuthService,
    private reclamationService: ReclamationService,
    private userService: UserService,
    private router: Router
  ) {}

  ngOnInit() {
    this.currentUser = this.authService.currentUserValue;
    
    if (!this.currentUser) {
      this.router.navigate(['/auth']);
      return;
    }

    // Vérifier si l'utilisateur est admin ou agent
    if (this.currentUser.role !== 'ADMIN' && this.currentUser.role !== 'AGENT') {
      this.router.navigate(['/dashboard']);
      return;
    }

    this.loadAllReclamations();
    this.loadAllUsers();
  }

  loadAllReclamations() {
    this.isLoading = true;
    this.reclamationService.getAllReclamations().subscribe({
      next: (reclamations) => {
        this.allReclamations = reclamations;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des réclamations:', error);
        this.errorMessage = 'Erreur lors du chargement des réclamations';
        this.isLoading = false;
      }
    });
  }

  loadAllUsers() {
    this.userService.getAllUsers().subscribe({
      next: (users) => {
        this.allUsers = users;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des utilisateurs:', error);
      }
    });
  }

  get filteredReclamations() {
    return this.allReclamations.filter(reclamation => {
      const matchesStatut = !this.selectedStatut || reclamation.statut === this.selectedStatut;
      const matchesType = !this.selectedType || reclamation.type === this.selectedType;
      const matchesSearch = !this.searchTerm || 
        reclamation.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        reclamation.description.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        (reclamation.user?.nom && reclamation.user.nom.toLowerCase().includes(this.searchTerm.toLowerCase()));
      
      return matchesStatut && matchesType && matchesSearch;
    });
  }

  openReponseModal(reclamation: Reclamation) {
    this.selectedReclamation = reclamation;
    this.reponseMessage = '';
    this.showReponseModal = true;
  }

  closeReponseModal() {
    this.showReponseModal = false;
    this.selectedReclamation = null;
    this.reponseMessage = '';
  }

  openStatutModal(reclamation: Reclamation) {
    this.selectedReclamation = reclamation;
    this.nouveauStatut = reclamation.statut || '';
    this.showStatutModal = true;
  }

  closeStatutModal() {
    this.showStatutModal = false;
    this.selectedReclamation = null;
    this.nouveauStatut = '';
  }

  envoyerReponse() {
    if (!this.selectedReclamation || !this.reponseMessage.trim() || !this.currentUser) {
      return;
    }

    this.reclamationService.addReponseToReclamation(
      this.selectedReclamation.id!,
      this.reponseMessage,
      this.currentUser.id!
    ).subscribe({
      next: () => {
        this.successMessage = 'Réponse envoyée avec succès !';
        this.closeReponseModal();
        this.loadAllReclamations();
      },
      error: (error) => {
        console.error('Erreur lors de l\'envoi de la réponse:', error);
        this.errorMessage = 'Erreur lors de l\'envoi de la réponse';
      }
    });
  }

  changerStatut() {
    if (!this.selectedReclamation || !this.nouveauStatut) {
      return;
    }

    this.reclamationService.updateReclamationStatut(
      this.selectedReclamation.id!,
      this.nouveauStatut
    ).subscribe({
      next: () => {
        this.successMessage = 'Statut mis à jour avec succès !';
        this.closeStatutModal();
        this.loadAllReclamations();
      },
      error: (error) => {
        console.error('Erreur lors de la mise à jour du statut:', error);
        this.errorMessage = 'Erreur lors de la mise à jour du statut';
      }
    });
  }

  getTypeDisplayName(type: string): string {
    return this.reclamationService.getTypeDisplayName(type);
  }

  getStatutDisplayName(statut: string): string {
    return this.reclamationService.getStatutDisplayName(statut);
  }

  getStatutColor(statut: string): string {
    return this.reclamationService.getStatutColor(statut);
  }

  formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR') + ' à ' + date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
  }

  logout() {
    this.authService.logout().subscribe({
      next: () => {
        this.router.navigate(['/']);
      },
      error: (error) => {
        console.error('Erreur lors de la déconnexion:', error);
        this.router.navigate(['/']);
      }
    });
  }
}
