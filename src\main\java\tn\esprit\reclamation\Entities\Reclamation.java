package tn.esprit.reclamation.Entities;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;

import java.util.Date;
import java.util.List;

@Entity
public class Reclamation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String titre;

    @Lob
    private String description;

    @Enumerated(EnumType.STRING)
    private TypeReclamation type;

    @Enumerated(EnumType.STRING)
    private StatutReclamation statut;

    @Column(columnDefinition = "TEXT")
    private String pieceJointe; // Fichier en base64 ou chemin vers le fichier

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "date_creation", nullable = false, updatable = false)
    private Date dateCreation;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "date_mise_ajour")
    private Date dateMiseAjour;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_id", nullable = false)
    @JsonIgnoreProperties({"password", "reclamations"})
    private User user;

    // Relation avec les réponses
    @OneToMany(mappedBy = "reclamation", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @JsonIgnoreProperties({"reclamation"})
    private List<ReponseReclamation> reponses;

    // Méthodes de cycle de vie JPA
    @PrePersist
    protected void onCreate() {
        dateCreation = new Date();
        dateMiseAjour = new Date();
        if (statut == null) {
            statut = StatutReclamation.EN_ATTENTE; // Statut par défaut
        }
    }

    @PreUpdate
    protected void onUpdate() {
        dateMiseAjour = new Date();
    }

    // Getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public String getTitre() { return titre; }
    public void setTitre(String titre) { this.titre = titre; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public TypeReclamation getType() { return type; }
    public void setType(TypeReclamation type) { this.type = type; }
    public StatutReclamation getStatut() { return statut; }
    public void setStatut(StatutReclamation statut) { this.statut = statut; }
    public String getPieceJointe() { return pieceJointe; }
    public void setPieceJointe(String pieceJointe) { this.pieceJointe = pieceJointe; }
    public Date getDateCreation() { return dateCreation; }
    public void setDateCreation(Date dateCreation) { this.dateCreation = dateCreation; }
    public Date getDateMiseAjour() { return dateMiseAjour; }
    public void setDateMiseAjour(Date dateMiseAjour) { this.dateMiseAjour = dateMiseAjour; }
    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }
    public List<ReponseReclamation> getReponses() { return reponses; }
    public void setReponses(List<ReponseReclamation> reponses) { this.reponses = reponses; }
}
