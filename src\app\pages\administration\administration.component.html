<div style="min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
  <!-- Header -->
  <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); padding: 1rem 2rem; border-bottom: 1px solid rgba(255,255,255,0.2);">
    <div style="display: flex; justify-content: space-between; align-items: center; max-width: 1200px; margin: 0 auto;">
      <div style="display: flex; align-items: center; gap: 1rem;">
        <h1 style="color: white; margin: 0; font-size: 1.8rem; font-weight: 600;">🛠️ Administration</h1>
        <span style="color: rgba(255,255,255,0.8); font-size: 0.9rem;"><PERSON><PERSON>rez les utilisateurs et les réclamations</span>
      </div>
      
      <div style="display: flex; align-items: center; gap: 1rem;">
        <span style="color: white; font-weight: 500;">👤 {{ currentUser?.nom }}</span>
        <button 
          (click)="logout()"
          style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px;"
        >
          Déconnexion
        </button>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div style="max-width: 1200px; margin: 0 auto; padding: 2rem;">
    
    <!-- Messages -->
    <div *ngIf="successMessage" style="background: #d4edda; color: #155724; padding: 12px; border-radius: 8px; margin-bottom: 1rem; border: 1px solid #c3e6cb;">
      {{ successMessage }}
    </div>
    
    <div *ngIf="errorMessage" style="background: #f8d7da; color: #721c24; padding: 12px; border-radius: 8px; margin-bottom: 1rem; border: 1px solid #f5c6cb;">
      {{ errorMessage }}
    </div>

    <!-- Filtres -->
    <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 1.5rem; margin-bottom: 2rem;">
      <h3 style="margin: 0 0 1rem 0; color: #333;">🔍 Filtres</h3>
      
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
        <div>
          <label style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Recherche</label>
          <input 
            type="text" 
            [(ngModel)]="searchTerm"
            placeholder="Titre, description, nom du client..."
            style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box;"
          >
        </div>
        
        <div>
          <label style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Statut</label>
          <select 
            [(ngModel)]="selectedStatut"
            style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box;"
          >
            <option value="">Tous les statuts</option>
            <option *ngFor="let statut of statuts" [value]="statut.value">{{ statut.label }}</option>
          </select>
        </div>
        
        <div>
          <label style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Type</label>
          <select 
            [(ngModel)]="selectedType"
            style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box;"
          >
            <option value="">Tous les types</option>
            <option *ngFor="let type of types" [value]="type.value">{{ type.label }}</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Liste des réclamations -->
    <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 2rem;">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
        <h3 style="margin: 0; color: #333;">📋 Toutes les Réclamations</h3>
        <span style="color: #666; font-size: 0.9rem;">{{ filteredReclamations.length }} réclamation(s)</span>
      </div>

      <div *ngIf="isLoading" style="text-align: center; padding: 2rem; color: #666;">
        Chargement des réclamations...
      </div>

      <div *ngIf="!isLoading && filteredReclamations.length === 0" style="text-align: center; padding: 2rem; color: #666;">
        Aucune réclamation trouvée.
      </div>

      <div *ngIf="!isLoading && filteredReclamations.length > 0" style="display: grid; gap: 1.5rem;">
        <div *ngFor="let reclamation of filteredReclamations" 
             style="border: 1px solid #e9ecef; border-radius: 8px; padding: 1.5rem; background: #f8f9fa;">
          
          <div style="display: flex; justify-content: between; align-items: start; margin-bottom: 1rem;">
            <div style="flex: 1;">
              <h4 style="color: #333; margin: 0 0 0.5rem 0; font-size: 1.2rem;">{{ reclamation.titre }}</h4>
              <div style="display: flex; gap: 1rem; align-items: center; margin-bottom: 0.5rem; flex-wrap: wrap;">
                <span style="font-size: 0.9rem; color: #666;">{{ getTypeDisplayName(reclamation.type) }}</span>
                <span 
                  style="padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; color: white;"
                  [style.background-color]="getStatutColor(reclamation.statut || '')"
                >
                  {{ getStatutDisplayName(reclamation.statut || '') }}
                </span>
                <span style="font-size: 0.9rem; color: #666;">👤 {{ reclamation.user?.nom }}</span>
              </div>
              <div style="font-size: 0.9rem; color: #666;">
                Créée le {{ formatDate(reclamation.dateCreation || '') }}
              </div>
            </div>
            
            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
              <button 
                (click)="openReponseModal(reclamation)"
                style="background: #28a745; color: white; border: none; padding: 8px 12px; border-radius: 6px; cursor: pointer; font-size: 12px;"
              >
                💬 Répondre
              </button>
              <button 
                (click)="openStatutModal(reclamation)"
                style="background: #007bff; color: white; border: none; padding: 8px 12px; border-radius: 6px; cursor: pointer; font-size: 12px;"
              >
                🔄 Statut
              </button>
            </div>
          </div>
          
          <p style="color: #555; margin: 0 0 1rem 0; line-height: 1.5;">{{ reclamation.description }}</p>
          
          <!-- Pièce jointe -->
          <div *ngIf="reclamation.pieceJointe" style="margin-top: 1rem; padding: 10px; background: #e9f7ef; border-radius: 6px; border: 1px solid #c3e6cb;">
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="color: #28a745; font-weight: 500;">📎</span>
              <span style="color: #155724; font-size: 14px; font-weight: 500;">Pièce jointe disponible</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal Réponse -->
  <div *ngIf="showReponseModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 1000;">
    <div style="background: white; border-radius: 12px; padding: 2rem; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
      <h3 style="margin: 0 0 1rem 0; color: #333;">💬 Répondre à la réclamation</h3>

      <div style="margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
        <h4 style="margin: 0 0 0.5rem 0; color: #333;">{{ selectedReclamation?.titre }}</h4>
        <p style="margin: 0; color: #666; font-size: 0.9rem;">{{ selectedReclamation?.description }}</p>
      </div>

      <div style="margin-bottom: 1.5rem;">
        <label style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Votre réponse</label>
        <textarea
          [(ngModel)]="reponseMessage"
          placeholder="Tapez votre réponse ici..."
          style="width: 100%; min-height: 120px; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box; resize: vertical;"
        ></textarea>
      </div>

      <div style="display: flex; gap: 1rem; justify-content: flex-end;">
        <button
          (click)="closeReponseModal()"
          style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500;"
        >
          Annuler
        </button>
        <button
          (click)="envoyerReponse()"
          [disabled]="!reponseMessage.trim()"
          style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500;"
          [style.opacity]="!reponseMessage.trim() ? '0.5' : '1'"
        >
          Envoyer la réponse
        </button>
      </div>
    </div>
  </div>

  <!-- Modal Changement de Statut -->
  <div *ngIf="showStatutModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 1000;">
    <div style="background: white; border-radius: 12px; padding: 2rem; max-width: 500px; width: 90%;">
      <h3 style="margin: 0 0 1rem 0; color: #333;">🔄 Changer le statut</h3>

      <div style="margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
        <h4 style="margin: 0 0 0.5rem 0; color: #333;">{{ selectedReclamation?.titre }}</h4>
        <span style="font-size: 0.9rem; color: #666;">Statut actuel: </span>
        <span
          style="padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; color: white;"
          [style.background-color]="getStatutColor(selectedReclamation?.statut || '')"
        >
          {{ getStatutDisplayName(selectedReclamation?.statut || '') }}
        </span>
      </div>

      <div style="margin-bottom: 1.5rem;">
        <label style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Nouveau statut</label>
        <select
          [(ngModel)]="nouveauStatut"
          style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box;"
        >
          <option *ngFor="let statut of statuts" [value]="statut.value">{{ statut.label }}</option>
        </select>
      </div>

      <div style="display: flex; gap: 1rem; justify-content: flex-end;">
        <button
          (click)="closeStatutModal()"
          style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500;"
        >
          Annuler
        </button>
        <button
          (click)="changerStatut()"
          [disabled]="!nouveauStatut"
          style="background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500;"
          [style.opacity]="!nouveauStatut ? '0.5' : '1'"
        >
          Mettre à jour
        </button>
      </div>
    </div>
  </div>
</div>
