<div style="min-height: 100vh; background: #f8f9fa;">
  <!-- Header -->
  <header style="background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 15px 0; position: fixed; top: 0; width: 100%; z-index: 1000;">
    <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center;">
      <!-- Logo -->
      <a routerLink="/" style="text-decoration: none;">
        <h1 style="color: #0ea2bd; font-size: 28px; font-weight: 700; margin: 0;">Dewi</h1>
      </a>

      <!-- User Info & Navigation -->
      <div style="display: flex; align-items: center; gap: 15px;">
        <!-- Mini Profile Image -->
        <div style="width: 40px; height: 40px; border-radius: 50%; overflow: hidden; border: 2px solid #007bff;">
          <img
            [src]="currentUser?.profileImage || 'assets/img/default-avatar.svg'"
            [alt]="currentUser?.nom + ' - Photo de profil'"
            style="width: 100%; height: 100%; object-fit: cover;"
            (error)="onImageError($event)"
          />
        </div>
        <span style="color: #333; font-weight: 500;">
          Bonjour, {{ currentUser?.nom }}
        </span>
        <span 
          style="padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; color: white;"
          [style.background-color]="getRoleColor(currentUser?.role || '')"
        >
          {{ getRoleDisplayName(currentUser?.role || '') }}
        </span>
        <button 
          (click)="logout()"
          style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-weight: 500;"
        >
          Déconnexion
        </button>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div style="padding-top: 100px; padding: 100px 20px 40px;">
    <div style="max-width: 1200px; margin: 0 auto;">
      
      <!-- Welcome Section -->
      <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 2rem; margin-bottom: 2rem;">
        <h1 style="color: #333; margin-bottom: 1rem;">Tableau de bord</h1>
        <p style="color: #666; font-size: 1.1rem;">
          Bienvenue sur votre espace personnel, {{ currentUser?.nom }} !
        </p>
        
        <!-- User Details -->
        <div style="margin-top: 2rem; padding: 1.5rem; background: #f8f9fa; border-radius: 8px;">
          <h3 style="color: #333; margin-bottom: 1rem;">Informations du compte</h3>

          <!-- Profile Section with Image -->
          <div style="display: flex; align-items: flex-start; gap: 2rem; margin-bottom: 1.5rem;">
            <!-- Profile Image -->
            <div style="flex-shrink: 0;">
              <div style="width: 100px; height: 100px; border-radius: 50%; overflow: hidden; border: 3px solid #007bff; box-shadow: 0 4px 12px rgba(0,123,255,0.3);">
                <img
                  [src]="currentUser?.profileImage || 'assets/img/default-avatar.svg'"
                  [alt]="currentUser?.nom + ' - Photo de profil'"
                  style="width: 100%; height: 100%; object-fit: cover;"
                  (error)="onImageError($event)"
                />
              </div>
            </div>

            <!-- User Info Grid -->
            <div style="flex: 1;">
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                <div>
                  <strong style="color: #555;">Nom :</strong>
                  <span style="margin-left: 10px;">{{ currentUser?.nom }}</span>
                </div>
                <div>
                  <strong style="color: #555;">Email :</strong>
                  <span style="margin-left: 10px;">{{ currentUser?.email }}</span>
                </div>
                <div>
                  <strong style="color: #555;">Rôle :</strong>
                  <span
                    style="margin-left: 10px; padding: 2px 8px; border-radius: 12px; font-size: 14px; font-weight: 600; color: white;"
                    [style.background-color]="getRoleColor(currentUser?.role || '')"
                  >
                    {{ getRoleDisplayName(currentUser?.role || '') }}
                  </span>
                </div>
                <div>
                  <strong style="color: #555;">Date de création :</strong>
                  <span style="margin-left: 10px;">{{ currentUser?.dateCreation | date:'dd/MM/yyyy' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions Section -->
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
        
        <!-- Réclamations -->
        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 2rem;">
          <div style="display: flex; align-items: center; margin-bottom: 1rem;">
            <div style="width: 50px; height: 50px; background: #0ea2bd; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
              <span style="color: white; font-size: 24px;">📋</span>
            </div>
            <h3 style="color: #333; margin: 0;">Réclamations</h3>
          </div>
          <p style="color: #666; margin-bottom: 1.5rem;">
            Gérez vos réclamations et suivez leur statut.
          </p>
          <button
            routerLink="/reclamations"
            style="background: #0ea2bd; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500; width: 100%;"
          >
            Voir les réclamations
          </button>
        </div>

        <!-- Profil -->
        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 2rem;">
          <div style="display: flex; align-items: center; margin-bottom: 1rem;">
            <div style="width: 50px; height: 50px; background: #28a745; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
              <span style="color: white; font-size: 24px;">👤</span>
            </div>
            <h3 style="color: #333; margin: 0;">Mon Profil</h3>
          </div>
          <p style="color: #666; margin-bottom: 1.5rem;">
            Modifiez vos informations personnelles.
          </p>
          <button
            routerLink="/profile"
            style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500; width: 100%;"
          >
            Modifier le profil
          </button>
        </div>

        <!-- Support (si ADMIN ou AGENT) -->
        <div 
          *ngIf="currentUser?.role === 'ADMIN' || currentUser?.role === 'AGENT'"
          style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 2rem;"
        >
          <div style="display: flex; align-items: center; margin-bottom: 1rem;">
            <div style="width: 50px; height: 50px; background: #fd7e14; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
              <span style="color: white; font-size: 24px;">🛠️</span>
            </div>
            <h3 style="color: #333; margin: 0;">Administration</h3>
          </div>
          <p style="color: #666; margin-bottom: 1.5rem;">
            Gérez les utilisateurs et les réclamations.
          </p>
          <button 
            style="background: #fd7e14; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500; width: 100%;"
          >
            Panneau d'admin
          </button>
        </div>

      </div>
    </div>
  </div>
</div>
