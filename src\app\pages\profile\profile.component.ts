import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { UserService, User } from '../../services/user.service';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterLink],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit {
  currentUser: User | null = null;
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  
  // Formulaire de modification du profil
  profileForm: User = {
    nom: '',
    email: '',
    password: '',
    role: '',
    dateCreation: '',
    profileImage: ''
  };

  // Formulaire de changement de mot de passe
  passwordForm = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  };

  // Gestion de l'image de profil
  selectedFile: File | null = null;
  imagePreview: string | null = null;

  activeTab = 'profile'; // 'profile' ou 'password'

  constructor(
    private authService: AuthService,
    private userService: UserService,
    private router: Router
  ) {}

  ngOnInit() {
    this.currentUser = this.authService.currentUserValue;
    
    if (!this.currentUser) {
      this.router.navigate(['/auth']);
      return;
    }

    this.initializeForm();
  }

  initializeForm() {
    if (this.currentUser) {
      this.profileForm = {
        nom: this.currentUser.nom,
        email: this.currentUser.email,
        password: '',
        role: this.currentUser.role,
        dateCreation: this.currentUser.dateCreation,
        profileImage: this.currentUser.profileImage
      };

      // Initialiser l'aperçu de l'image si elle existe
      if (this.currentUser.profileImage) {
        this.imagePreview = this.currentUser.profileImage;
      }
    }
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    this.clearMessages();
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      this.selectedFile = file;
      
      // Créer un aperçu de l'image
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.imagePreview = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  }

  onSubmitProfile() {
    if (!this.currentUser?.id) return;
    
    if (!this.isProfileFormValid()) {
      this.errorMessage = 'Veuillez remplir tous les champs obligatoires';
      return;
    }

    this.isLoading = true;
    this.clearMessages();

    // Créer un objet utilisateur mis à jour
    const updatedUser: User = {
      ...this.profileForm,
      id: this.currentUser.id,
      // Inclure l'image de profil si elle a été modifiée
      profileImage: this.imagePreview || this.currentUser.profileImage
    };

    this.userService.updateUser(this.currentUser.id, updatedUser).subscribe({
      next: (user) => {
        if (user) {
          this.successMessage = 'Profil mis à jour avec succès !';

          // Mettre à jour l'utilisateur dans le service d'authentification
          const updatedCurrentUser = { ...this.currentUser, ...user };
          localStorage.setItem('currentUser', JSON.stringify(updatedCurrentUser));
          this.authService['currentUserSubject'].next(updatedCurrentUser);
          this.currentUser = updatedCurrentUser;

          // Recharger les données du formulaire avec les nouvelles valeurs
          this.initializeForm();
        } else {
          this.errorMessage = 'Utilisateur non trouvé. Veuillez vous reconnecter.';
        }

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erreur lors de la mise à jour du profil:', error);
        if (error.status === 404) {
          this.errorMessage = 'Utilisateur non trouvé. Veuillez vous reconnecter.';
        } else {
          this.errorMessage = error.error?.message || 'Erreur lors de la mise à jour du profil';
        }
        this.isLoading = false;
      }
    });
  }

  onSubmitPassword() {
    if (!this.currentUser?.id) return;
    
    if (!this.isPasswordFormValid()) {
      this.errorMessage = 'Veuillez remplir tous les champs et vérifier que les mots de passe correspondent';
      return;
    }

    this.isLoading = true;
    this.clearMessages();

    // TODO: Implémenter le changement de mot de passe
    // Pour l'instant, simulons une réponse
    setTimeout(() => {
      this.successMessage = 'Mot de passe changé avec succès !';
      this.resetPasswordForm();
      this.isLoading = false;
    }, 1000);
  }

  private isProfileFormValid(): boolean {
    return !!(
      this.profileForm.nom.trim() &&
      this.profileForm.email.trim() &&
      this.isValidEmail(this.profileForm.email)
    );
  }

  private isPasswordFormValid(): boolean {
    return !!(
      this.passwordForm.currentPassword &&
      this.passwordForm.newPassword &&
      this.passwordForm.confirmPassword &&
      this.passwordForm.newPassword === this.passwordForm.confirmPassword &&
      this.passwordForm.newPassword.length >= 6
    );
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private resetPasswordForm() {
    this.passwordForm = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    };
  }

  private clearMessages() {
    this.errorMessage = '';
    this.successMessage = '';
  }

  getRoleDisplayName(role: string): string {
    const roles: { [key: string]: string } = {
      'ADMIN': 'Administrateur',
      'AGENT': 'Agent',
      'CLIENT': 'Client'
    };
    return roles[role] || role;
  }

  getRoleColor(role: string): string {
    const colors: { [key: string]: string } = {
      'ADMIN': '#dc3545',
      'AGENT': '#fd7e14',
      'CLIENT': '#28a745'
    };
    return colors[role] || '#6c757d';
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  logout() {
    this.authService.logout().subscribe({
      next: () => {
        this.router.navigate(['/']);
      },
      error: (error) => {
        console.error('Erreur lors de la déconnexion:', error);
        this.router.navigate(['/']);
      }
    });
  }
}
