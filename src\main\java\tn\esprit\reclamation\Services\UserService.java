package tn.esprit.reclamation.Services;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import tn.esprit.reclamation.Entities.User;
import tn.esprit.reclamation.Repository.UserRepository;

import java.util.List;
import java.util.Optional;

@Service
public class UserService {
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    public User addUser(User user) throws RuntimeException {
        // Vérifier si l'email existe déjà
        if (userRepository.existsByEmail(user.getEmail())) {
            throw new RuntimeException("Un utilisateur avec cet email existe déjà");
        }

        // Valider les données
        if (user.getNom() == null || user.getNom().trim().isEmpty()) {
            throw new RuntimeException("Le nom est obligatoire");
        }

        if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
            throw new RuntimeException("L'email est obligatoire");
        }

        if (user.getPassword() == null || user.getPassword().length() < 6) {
            throw new RuntimeException("Le mot de passe doit contenir au moins 6 caractères");
        }

        if (user.getRole() == null) {
            throw new RuntimeException("Le rôle est obligatoire");
        }

        // Encoder le mot de passe avant de sauvegarder
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        user.setDateCreation(new java.util.Date());
        return userRepository.save(user);
    }

    // Méthode d'authentification avec BCrypt
    public User authenticate(String email, String password) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            // Vérifier le mot de passe avec BCrypt
            if (user.getPassword() != null && passwordEncoder.matches(password, user.getPassword())) {
                return user;
            }
        }
        return null;
    }

    // Vérifier si un email existe
    public boolean emailExists(String email) {
        return userRepository.existsByEmail(email);
    }

    // Rechercher par email
    public User findByEmail(String email) {
        return userRepository.findByEmail(email).orElse(null);
    }

    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    public User getUserById(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    public void deleteUser(Long id) {
        userRepository.deleteById(id);
    }

    public User updateUser(Long id, User user) {
        Optional<User> existing = userRepository.findById(id);
        if (existing.isPresent()) {
            User u = existing.get();
            u.setNom(user.getNom());
            u.setEmail(user.getEmail());

            // Encoder le nouveau mot de passe s'il est fourni
            if (user.getPassword() != null && !user.getPassword().trim().isEmpty()) {
                u.setPassword(passwordEncoder.encode(user.getPassword()));
            }

            // Mettre à jour l'image de profil si elle est fournie
            if (user.getProfileImage() != null) {
                u.setProfileImage(user.getProfileImage());
            }

            u.setRole(user.getRole());
            return userRepository.save(u);
        }
        return null;
    }
}
