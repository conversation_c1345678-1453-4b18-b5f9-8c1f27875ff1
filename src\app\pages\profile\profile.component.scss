// Styles pour le composant profil
.profile-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.header {
  background: white;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  padding: 15px 0;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
}

.main-content {
  padding-top: 100px;
  padding: 100px 20px 40px;
}

.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.profile-image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .placeholder {
    font-size: 2rem;
    color: #6c757d;
  }
}

.tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  overflow: hidden;
}

.tab-header {
  display: flex;
  border-bottom: 1px solid #e9ecef;
}

.tab-button {
  flex: 1;
  border: none;
  padding: 1rem 2rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &.active {
    background: #0ea2bd;
    color: white;
  }
  
  &:not(.active) {
    background: white;
    color: #333;
    
    &:hover {
      background: #f8f9fa;
    }
  }
}

.tab-content {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #555;
  margin-bottom: 8px;
}

.form-control {
  width: 100%;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 16px;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #0ea2bd;
  }
  
  &:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
  }
}

.btn {
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.3s ease;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.btn-primary {
  background: #0ea2bd;
  color: white;
  
  &:hover:not(:disabled) {
    background: #0c8ca3;
  }
}

.btn-success {
  background: #28a745;
  color: white;
  
  &:hover:not(:disabled) {
    background: #218838;
  }
}

.btn-warning {
  background: #fd7e14;
  color: white;
  
  &:hover:not(:disabled) {
    background: #e96b00;
  }
}

.btn-danger {
  background: #dc3545;
  color: white;
  
  &:hover:not(:disabled) {
    background: #c82333;
  }
}

.alert {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid;
}

.alert-danger {
  background: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

.alert-success {
  background: #d4edda;
  color: #155724;
  border-color: #c3e6cb;
}

.role-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.file-input {
  width: 100%;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 16px;
  box-sizing: border-box;
  
  &:focus {
    outline: none;
    border-color: #0ea2bd;
  }
}

.help-text {
  color: #666;
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

.error-text {
  color: #dc3545;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

// Responsive design
@media (max-width: 768px) {
  .main-content {
    padding: 100px 10px 20px;
  }
  
  .card {
    padding: 1rem;
  }
  
  .tab-content {
    padding: 1rem;
  }
  
  .tab-button {
    padding: 0.75rem 1rem;
    font-size: 14px;
  }
  
  .profile-image {
    width: 80px;
    height: 80px;
  }
}
