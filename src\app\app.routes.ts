import { Routes } from '@angular/router';
import { HomeComponent } from './pages/home/<USER>';
import { AboutComponent } from './pages/about/about.component';
import { ServicesComponent } from './pages/services/services.component';
import { PortfolioComponent } from './pages/portfolio/portfolio.component';
import { TeamComponent } from './pages/team/team.component';
import { PricingComponent } from './pages/pricing/pricing.component';
import { BlogComponent } from './pages/blog/blog.component';
import { ContactComponent } from './pages/contact/contact.component';
import { PortfolioDetailsComponent } from './pages/portfolio-details/portfolio-details.component';
import { ServiceDetailsComponent } from './pages/service-details/service-details.component';
import { StarterComponent } from './pages/starter/starter.component';
import { LoginComponent } from './pages/login/login.component';
import { AuthComponent } from './pages/auth/auth.component';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { TestConnectionComponent } from './pages/test-connection/test-connection.component';
import { OAuth2RedirectComponent } from './pages/oauth2-redirect/oauth2-redirect.component';
import { ReclamationsComponent } from './pages/reclamations/reclamations.component';
import { ProfileComponent } from './pages/profile/profile.component';

export const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'about', component: AboutComponent },
  { path: 'services', component: ServicesComponent },
  { path: 'portfolio', component: PortfolioComponent },
  { path: 'team', component: TeamComponent },
  { path: 'pricing', component: PricingComponent },
  { path: 'blog', component: BlogComponent },
  { path: 'contact', component: ContactComponent },
  { path: 'portfolio-details', component: PortfolioDetailsComponent },
  { path: 'service-details', component: ServiceDetailsComponent },
  { path: 'starter', component: StarterComponent },
  { path: 'login', component: LoginComponent }, // Page d'inscription
  { path: 'auth', component: AuthComponent }, // Page de connexion
  { path: 'dashboard', component: DashboardComponent }, // Tableau de bord
  { path: 'reclamations', component: ReclamationsComponent }, // Page des réclamations
  { path: 'profile', component: ProfileComponent }, // Page du profil
  { path: 'test-connection', component: TestConnectionComponent },
  { path: 'oauth2/redirect', component: OAuth2RedirectComponent } // OAuth2 redirect
];
