package tn.esprit.reclamation.Entities;

import jakarta.persistence.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Date;

@Entity
@Table(name = "reponse_reclamation")
public class ReponseReclamation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Lob
    @Column(columnDefinition = "TEXT")
    private String message;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "reclamation_id", nullable = false)
    @JsonIgnoreProperties({"reponses"})
    private Reclamation reclamation;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "agent_id", nullable = false)
    @JsonIgnoreProperties({"password", "reclamations"})
    private User agent; // L'agent qui a répondu

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "date_reponse", nullable = false, updatable = false)
    private Date dateReponse;

    @PrePersist
    protected void onCreate() {
        dateReponse = new Date();
    }

    // Constructeurs
    public ReponseReclamation() {}

    public ReponseReclamation(String message, Reclamation reclamation, User agent) {
        this.message = message;
        this.reclamation = reclamation;
        this.agent = agent;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Reclamation getReclamation() {
        return reclamation;
    }

    public void setReclamation(Reclamation reclamation) {
        this.reclamation = reclamation;
    }

    public User getAgent() {
        return agent;
    }

    public void setAgent(User agent) {
        this.agent = agent;
    }

    public Date getDateReponse() {
        return dateReponse;
    }

    public void setDateReponse(Date dateReponse) {
        this.dateReponse = dateReponse;
    }
}
