<div style="min-height: 100vh; background: #f8f9fa;">
  <!-- Header -->
  <header style="background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 15px 0; position: fixed; top: 0; width: 100%; z-index: 1000;">
    <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center;">
      <!-- Logo -->
      <a routerLink="/" style="text-decoration: none;">
        <h1 style="color: #0ea2bd; font-size: 28px; font-weight: 700; margin: 0;">Dewi</h1>
      </a>

      <!-- Navigation -->
      <div style="display: flex; align-items: center; gap: 20px;">
        <a routerLink="/dashboard" style="color: #333; text-decoration: none; font-weight: 500;">Dashboard</a>
        <span style="color: #333; font-weight: 500;">{{ currentUser?.nom }}</span>
        <button 
          (click)="logout()"
          style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-weight: 500;"
        >
          Déconnexion
        </button>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div style="padding-top: 100px; padding: 100px 20px 40px;">
    <div style="max-width: 1200px; margin: 0 auto;">
      
      <!-- Page Title -->
      <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 2rem; margin-bottom: 2rem;">
        <h1 style="color: #333; margin-bottom: 1rem; display: flex; align-items: center;">
          <span style="margin-right: 15px; font-size: 2rem;">👤</span>
          Mon Profil
        </h1>
        <p style="color: #666; font-size: 1.1rem;">
          Gérez vos informations personnelles et paramètres de compte.
        </p>
      </div>

      <!-- Profile Info Card -->
      <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 2rem; margin-bottom: 2rem;">
        <div style="display: flex; align-items: center; gap: 2rem; margin-bottom: 2rem;">
          <!-- Profile Image -->
          <div style="position: relative;">
            <div style="width: 100px; height: 100px; border-radius: 50%; background: #e9ecef; display: flex; align-items: center; justify-content: center; overflow: hidden;">
              <img *ngIf="imagePreview || currentUser?.profileImage" 
                   [src]="imagePreview || currentUser?.profileImage" 
                   alt="Profile" 
                   style="width: 100%; height: 100%; object-fit: cover;">
              <span *ngIf="!imagePreview && !currentUser?.profileImage" 
                    style="font-size: 2rem; color: #6c757d;">👤</span>
            </div>
          </div>
          
          <!-- User Info -->
          <div>
            <h3 style="color: #333; margin: 0 0 0.5rem 0;">{{ currentUser?.nom }}</h3>
            <p style="color: #666; margin: 0 0 0.5rem 0;">{{ currentUser?.email }}</p>
            <span 
              style="padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; color: white;"
              [style.background-color]="getRoleColor(currentUser?.role || '')"
            >
              {{ getRoleDisplayName(currentUser?.role || '') }}
            </span>
            <p style="color: #666; margin: 0.5rem 0 0 0; font-size: 0.9rem;">
              Membre depuis le {{ formatDate(currentUser?.dateCreation || '') }}
            </p>
          </div>
        </div>
      </div>

      <!-- Tabs -->
      <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); overflow: hidden;">
        <div style="display: flex; border-bottom: 1px solid #e9ecef;">
          <button 
            (click)="setActiveTab('profile')"
            [style.background]="activeTab === 'profile' ? '#0ea2bd' : 'white'"
            [style.color]="activeTab === 'profile' ? 'white' : '#333'"
            style="flex: 1; border: none; padding: 1rem 2rem; cursor: pointer; font-weight: 500; transition: all 0.3s ease;"
          >
            Informations personnelles
          </button>
          <button 
            (click)="setActiveTab('password')"
            [style.background]="activeTab === 'password' ? '#0ea2bd' : 'white'"
            [style.color]="activeTab === 'password' ? 'white' : '#333'"
            style="flex: 1; border: none; padding: 1rem 2rem; cursor: pointer; font-weight: 500; transition: all 0.3s ease;"
          >
            Changer le mot de passe
          </button>
        </div>

        <div style="padding: 2rem;">
          <!-- Messages -->
          <div *ngIf="errorMessage" style="background: #f8d7da; color: #721c24; padding: 12px; border-radius: 8px; margin-bottom: 1rem; border: 1px solid #f5c6cb;">
            {{ errorMessage }}
          </div>
          <div *ngIf="successMessage" style="background: #d4edda; color: #155724; padding: 12px; border-radius: 8px; margin-bottom: 1rem; border: 1px solid #c3e6cb;">
            {{ successMessage }}
          </div>

          <!-- Profile Tab -->
          <div *ngIf="activeTab === 'profile'">
            <form (ngSubmit)="onSubmitProfile()" #profileFormRef="ngForm">
              <!-- Image Upload -->
              <div style="margin-bottom: 2rem;">
                <label style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Photo de profil</label>
                <input 
                  type="file" 
                  accept="image/*"
                  (change)="onFileSelected($event)"
                  style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box;"
                >
                <small style="color: #666; font-size: 0.9rem;">Formats acceptés: JPG, PNG, GIF (max 5MB)</small>
              </div>

              <div style="margin-bottom: 1.5rem;">
                <label for="nom" style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Nom complet *</label>
                <input 
                  type="text" 
                  id="nom" 
                  name="nom" 
                  [(ngModel)]="profileForm.nom" 
                  required
                  [disabled]="isLoading"
                  style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box;"
                  placeholder="Votre nom complet"
                >
              </div>

              <div style="margin-bottom: 1.5rem;">
                <label for="email" style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Email *</label>
                <input 
                  type="email" 
                  id="email" 
                  name="email" 
                  [(ngModel)]="profileForm.email" 
                  required
                  [disabled]="isLoading"
                  style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box;"
                  placeholder="<EMAIL>"
                >
              </div>

              <button 
                type="submit"
                [disabled]="isLoading || !profileFormRef.form.valid"
                style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500; font-size: 16px;"
              >
                {{ isLoading ? 'Mise à jour...' : 'Mettre à jour le profil' }}
              </button>
            </form>
          </div>

          <!-- Password Tab -->
          <div *ngIf="activeTab === 'password'">
            <form (ngSubmit)="onSubmitPassword()" #passwordFormRef="ngForm">
              <div style="margin-bottom: 1.5rem;">
                <label for="currentPassword" style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Mot de passe actuel *</label>
                <input 
                  type="password" 
                  id="currentPassword" 
                  name="currentPassword" 
                  [(ngModel)]="passwordForm.currentPassword" 
                  required
                  [disabled]="isLoading"
                  style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box;"
                  placeholder="Votre mot de passe actuel"
                >
              </div>

              <div style="margin-bottom: 1.5rem;">
                <label for="newPassword" style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Nouveau mot de passe *</label>
                <input 
                  type="password" 
                  id="newPassword" 
                  name="newPassword" 
                  [(ngModel)]="passwordForm.newPassword" 
                  required
                  minlength="6"
                  [disabled]="isLoading"
                  style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box;"
                  placeholder="Nouveau mot de passe (min 6 caractères)"
                >
              </div>

              <div style="margin-bottom: 1.5rem;">
                <label for="confirmPassword" style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Confirmer le nouveau mot de passe *</label>
                <input 
                  type="password" 
                  id="confirmPassword" 
                  name="confirmPassword" 
                  [(ngModel)]="passwordForm.confirmPassword" 
                  required
                  [disabled]="isLoading"
                  style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box;"
                  placeholder="Confirmez le nouveau mot de passe"
                >
              </div>

              <div *ngIf="passwordForm.newPassword && passwordForm.confirmPassword && passwordForm.newPassword !== passwordForm.confirmPassword" 
                   style="color: #dc3545; font-size: 0.9rem; margin-bottom: 1rem;">
                Les mots de passe ne correspondent pas
              </div>

              <button 
                type="submit"
                [disabled]="isLoading || !passwordFormRef.form.valid || passwordForm.newPassword !== passwordForm.confirmPassword"
                style="background: #fd7e14; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500; font-size: 16px;"
              >
                {{ isLoading ? 'Changement...' : 'Changer le mot de passe' }}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
