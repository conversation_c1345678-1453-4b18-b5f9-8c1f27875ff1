import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface Reclamation {
  id?: number;
  titre: string;
  description: string;
  type: string;
  statut?: string;
  pieceJointe?: string;
  dateCreation?: string;
  dateMiseAjour?: string;
  user?: any;
}

export interface ReclamationStats {
  total: number;
  nouvelles: number;
  enCours: number;
  resolues: number;
}

@Injectable({
  providedIn: 'root'
})
export class ReclamationService {
  private apiUrl = 'http://localhost:8081/api/reclamations';

  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json'
    })
  };

  constructor(private http: HttpClient) { }

  // Récupérer toutes les réclamations
  getAllReclamations(): Observable<Reclamation[]> {
    return this.http.get<Reclamation[]>(this.apiUrl);
  }

  // Récupérer une réclamation par ID
  getReclamationById(id: number): Observable<Reclamation> {
    return this.http.get<Reclamation>(`${this.apiUrl}/${id}`);
  }

  // Créer une nouvelle réclamation
  createReclamation(reclamation: Reclamation): Observable<Reclamation> {
    return this.http.post<Reclamation>(this.apiUrl, reclamation, this.httpOptions);
  }

  // Supprimer une réclamation
  deleteReclamation(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  // Récupérer les réclamations d'un utilisateur
  getReclamationsByUser(userId: number): Observable<Reclamation[]> {
    return this.http.get<Reclamation[]>(`${this.apiUrl}/user/${userId}`);
  }

  // Créer une réclamation pour un utilisateur
  createReclamationForUser(userId: number, reclamation: Reclamation): Observable<Reclamation> {
    return this.http.post<Reclamation>(`${this.apiUrl}/user/${userId}`, reclamation, this.httpOptions);
  }

  // Récupérer les statistiques des réclamations d'un utilisateur
  getUserReclamationStats(userId: number): Observable<ReclamationStats> {
    return this.http.get<ReclamationStats>(`${this.apiUrl}/user/${userId}/stats`);
  }

  // Méthodes utilitaires
  getTypeDisplayName(type: string): string {
    const types: { [key: string]: string } = {
      'PROBLEME_RESEAU': 'Problème Réseau',
      'FACTURATION': 'Facturation',
      'ABONNEMENT': 'Abonnement',
      'SERVICE_CLIENT': 'Service Client',
      'AUTRE': 'Autre'
    };
    return types[type] || type;
  }

  getStatutDisplayName(statut: string): string {
    const statuts: { [key: string]: string } = {
      'NOUVELLE': 'Nouvelle',
      'EN_COURS': 'En cours',
      'RESOLUE': 'Résolue'
    };
    return statuts[statut] || statut;
  }

  getStatutColor(statut: string): string {
    const colors: { [key: string]: string } = {
      'NOUVELLE': '#ffc107',
      'EN_COURS': '#17a2b8',
      'RESOLUE': '#28a745'
    };
    return colors[statut] || '#6c757d';
  }
}
