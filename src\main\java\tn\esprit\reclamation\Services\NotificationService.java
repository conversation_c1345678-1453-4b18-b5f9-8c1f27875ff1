package tn.esprit.reclamation.Services;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tn.esprit.reclamation.Entities.Notification;
import tn.esprit.reclamation.Entities.Reclamation;
import tn.esprit.reclamation.Entities.User;
import tn.esprit.reclamation.Repository.NotificationRepository;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class NotificationService {
    
    @Autowired
    private NotificationRepository notificationRepository;

    // Méthodes CRUD de base
    public List<Notification> getAllNotifications() {
        return notificationRepository.findAll();
    }

    public Optional<Notification> getNotificationById(Long id) {
        return notificationRepository.findById(id);
    }

    public Notification saveNotification(Notification notification) {
        return notificationRepository.save(notification);
    }

    public void deleteNotification(Long id) {
        notificationRepository.deleteById(id);
    }

    // Méthodes spécifiques
    public List<Notification> getNotificationsByUser(User user) {
        return notificationRepository.findByUserOrderByDateCreationDesc(user);
    }

    public List<Notification> getUnreadNotificationsByUser(User user) {
        return notificationRepository.findByUserAndLuFalseOrderByDateCreationDesc(user);
    }

    public long countUnreadNotificationsByUser(User user) {
        return notificationRepository.countByUserAndLuFalse(user);
    }

    public void markAsRead(Long notificationId) {
        Optional<Notification> notification = notificationRepository.findById(notificationId);
        if (notification.isPresent()) {
            notification.get().setLu(true);
            notificationRepository.save(notification.get());
        }
    }

    public void markAllAsReadForUser(User user) {
        List<Notification> unreadNotifications = getUnreadNotificationsByUser(user);
        for (Notification notification : unreadNotifications) {
            notification.setLu(true);
            notificationRepository.save(notification);
        }
    }

    // Méthodes pour créer des notifications spécifiques
    public Notification createReponseNotification(User client, Reclamation reclamation, String agentNom) {
        String titre = "Nouvelle réponse à votre réclamation";
        String message = String.format("L'agent %s a répondu à votre réclamation \"%s\"", 
                                      agentNom, reclamation.getTitre());
        
        Notification notification = new Notification(titre, message, "REPONSE", client, reclamation);
        return saveNotification(notification);
    }

    public Notification createStatutChangeNotification(User client, Reclamation reclamation, String ancienStatut, String nouveauStatut) {
        String titre = "Statut de réclamation modifié";
        String message = String.format("Le statut de votre réclamation \"%s\" est passé de \"%s\" à \"%s\"", 
                                      reclamation.getTitre(), ancienStatut, nouveauStatut);
        
        Notification notification = new Notification(titre, message, "STATUT_CHANGE", client, reclamation);
        return saveNotification(notification);
    }

    public Notification createReclamationCreatedNotification(User client, Reclamation reclamation) {
        String titre = "Réclamation créée avec succès";
        String message = String.format("Votre réclamation \"%s\" a été créée et sera traitée dans les plus brefs délais", 
                                      reclamation.getTitre());
        
        Notification notification = new Notification(titre, message, "NOUVELLE_RECLAMATION", client, reclamation);
        return saveNotification(notification);
    }
}
