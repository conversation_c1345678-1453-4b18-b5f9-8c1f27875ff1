<div style="min-height: 100vh; background: #f8f9fa;">
  <!-- Header -->
  <header style="background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 15px 0; position: fixed; top: 0; width: 100%; z-index: 1000;">
    <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center;">
      <!-- Logo -->
      <a routerLink="/" style="text-decoration: none;">
        <h1 style="color: #0ea2bd; font-size: 28px; font-weight: 700; margin: 0;">Dewi</h1>
      </a>

      <!-- Navigation -->
      <div style="display: flex; align-items: center; gap: 20px;">
        <a routerLink="/dashboard" style="color: #333; text-decoration: none; font-weight: 500;">Dashboard</a>
        <span style="color: #333; font-weight: 500;">{{ currentUser?.nom }}</span>
        <button 
          (click)="logout()"
          style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-weight: 500;"
        >
          Déconnexion
        </button>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div style="padding-top: 100px; padding: 100px 20px 40px;">
    <div style="max-width: 1200px; margin: 0 auto;">
      
      <!-- Page Title -->
      <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 2rem; margin-bottom: 2rem;">
        <h1 style="color: #333; margin-bottom: 1rem; display: flex; align-items: center;">
          <span style="margin-right: 15px; font-size: 2rem;">📋</span>
          Mes Réclamations
        </h1>
        <p style="color: #666; font-size: 1.1rem;">
          Gérez vos réclamations et suivez leur statut en temps réel.
        </p>
      </div>

      <!-- Statistics -->
      <div *ngIf="stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 1.5rem; text-align: center;">
          <div style="font-size: 2rem; font-weight: bold; color: #0ea2bd;">{{ stats.total }}</div>
          <div style="color: #666; margin-top: 0.5rem;">Total</div>
        </div>
        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 1.5rem; text-align: center;">
          <div style="font-size: 2rem; font-weight: bold; color: #ffc107;">{{ stats.nouvelles }}</div>
          <div style="color: #666; margin-top: 0.5rem;">Nouvelles</div>
        </div>
        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 1.5rem; text-align: center;">
          <div style="font-size: 2rem; font-weight: bold; color: #17a2b8;">{{ stats.enCours }}</div>
          <div style="color: #666; margin-top: 0.5rem;">En cours</div>
        </div>
        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 1.5rem; text-align: center;">
          <div style="font-size: 2rem; font-weight: bold; color: #28a745;">{{ stats.resolues }}</div>
          <div style="color: #666; margin-top: 0.5rem;">Résolues</div>
        </div>
      </div>

      <!-- New Reclamation Button -->
      <div style="margin-bottom: 2rem;">
        <button 
          (click)="toggleNewReclamationForm()"
          style="background: #0ea2bd; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500; font-size: 16px;"
        >
          {{ showNewReclamationForm ? 'Annuler' : '+ Nouvelle Réclamation' }}
        </button>
      </div>

      <!-- New Reclamation Form -->
      <div *ngIf="showNewReclamationForm" style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 2rem; margin-bottom: 2rem;">
        <h3 style="color: #333; margin-bottom: 1.5rem;">Nouvelle Réclamation</h3>
        
        <!-- Messages -->
        <div *ngIf="errorMessage" style="background: #f8d7da; color: #721c24; padding: 12px; border-radius: 8px; margin-bottom: 1rem; border: 1px solid #f5c6cb;">
          {{ errorMessage }}
        </div>
        <div *ngIf="successMessage" style="background: #d4edda; color: #155724; padding: 12px; border-radius: 8px; margin-bottom: 1rem; border: 1px solid #c3e6cb;">
          {{ successMessage }}
        </div>

        <form (ngSubmit)="onSubmitReclamation()" #reclamationForm="ngForm">
          <div style="margin-bottom: 1.5rem;">
            <label for="titre" style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Titre *</label>
            <input 
              type="text" 
              id="titre" 
              name="titre" 
              [(ngModel)]="newReclamation.titre" 
              required
              [disabled]="isLoading"
              style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box;"
              placeholder="Titre de votre réclamation"
            >
          </div>

          <div style="margin-bottom: 1.5rem;">
            <label for="type" style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Type *</label>
            <select 
              id="type" 
              name="type" 
              [(ngModel)]="newReclamation.type" 
              required
              [disabled]="isLoading"
              style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box;"
            >
              <option *ngFor="let type of types" [value]="type.value">{{ type.label }}</option>
            </select>
          </div>

          <div style="margin-bottom: 1.5rem;">
            <label for="description" style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Description *</label>
            <textarea 
              id="description" 
              name="description" 
              [(ngModel)]="newReclamation.description" 
              required
              [disabled]="isLoading"
              rows="5"
              style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box; resize: vertical;"
              placeholder="Décrivez votre problème en détail..."
            ></textarea>
          </div>

          <!-- Pièce jointe -->
          <div style="margin-bottom: 1.5rem;">
            <label for="pieceJointe" style="display: block; font-weight: 500; color: #555; margin-bottom: 8px;">Pièce jointe (optionnel)</label>
            <input
              type="file"
              id="pieceJointe"
              name="pieceJointe"
              (change)="onFileSelected($event)"
              [disabled]="isLoading"
              accept="image/*,.pdf,.doc,.docx,.txt"
              style="width: 100%; border: 2px solid #e9ecef; border-radius: 8px; padding: 12px 15px; font-size: 16px; box-sizing: border-box;"
            >
            <small style="color: #666; font-size: 14px;">Formats acceptés: Images, PDF, Word, Texte (max 5MB)</small>

            <!-- Aperçu du fichier sélectionné -->
            <div *ngIf="selectedFileName" style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 6px; border: 1px solid #dee2e6;">
              <div style="display: flex; align-items: center; gap: 10px;">
                <span style="color: #28a745; font-weight: 500;">📎</span>
                <span style="color: #333; font-size: 14px;">{{ selectedFileName }}</span>
                <button
                  type="button"
                  (click)="removeSelectedFile()"
                  style="background: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px; margin-left: auto;"
                >
                  Supprimer
                </button>
              </div>
            </div>
          </div>

          <button
            type="submit"
            [disabled]="isLoading || !reclamationForm.form.valid"
            style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500; font-size: 16px;"
          >
            {{ isLoading ? 'Création...' : 'Créer la réclamation' }}
          </button>
        </form>
      </div>

      <!-- Reclamations List -->
      <div style="background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); padding: 2rem;">
        <h3 style="color: #333; margin-bottom: 1.5rem;">Liste des réclamations</h3>
        
        <div *ngIf="isLoading && !showNewReclamationForm" style="text-align: center; padding: 2rem; color: #666;">
          Chargement des réclamations...
        </div>

        <div *ngIf="!isLoading && reclamations.length === 0" style="text-align: center; padding: 2rem; color: #666;">
          Aucune réclamation trouvée. Créez votre première réclamation !
        </div>

        <div *ngIf="!isLoading && reclamations.length > 0" style="display: grid; gap: 1.5rem;">
          <div *ngFor="let reclamation of reclamations" 
               style="border: 1px solid #e9ecef; border-radius: 8px; padding: 1.5rem; background: #f8f9fa;">
            
            <div style="display: flex; justify-content: between; align-items: start; margin-bottom: 1rem;">
              <div style="flex: 1;">
                <h4 style="color: #333; margin: 0 0 0.5rem 0; font-size: 1.2rem;">{{ reclamation.titre }}</h4>
                <div style="display: flex; gap: 1rem; align-items: center; margin-bottom: 0.5rem;">
                  <span style="font-size: 0.9rem; color: #666;">{{ getTypeDisplayName(reclamation.type) }}</span>
                  <span 
                    style="padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; color: white;"
                    [style.background-color]="getStatutColor(reclamation.statut || '')"
                  >
                    {{ getStatutDisplayName(reclamation.statut || '') }}
                  </span>
                </div>
                <div style="font-size: 0.9rem; color: #666;">
                  Créée le {{ formatDate(reclamation.dateCreation || '') }}
                </div>
              </div>
            </div>

            <p style="color: #555; margin: 0 0 1rem 0; line-height: 1.5;">{{ reclamation.description }}</p>

            <!-- Pièce jointe -->
            <div *ngIf="reclamation.pieceJointe" style="margin-top: 1rem; padding: 10px; background: #e9f7ef; border-radius: 6px; border: 1px solid #c3e6cb;">
              <div style="display: flex; align-items: center; gap: 10px;">
                <span style="color: #28a745; font-weight: 500;">📎</span>
                <span style="color: #155724; font-size: 14px; font-weight: 500;">Pièce jointe disponible</span>
                <button
                  type="button"
                  (click)="downloadAttachment(reclamation.pieceJointe || '', reclamation.titre)"
                  style="background: #28a745; color: white; border: none; padding: 4px 12px; border-radius: 4px; cursor: pointer; font-size: 12px; margin-left: auto;"
                >
                  Télécharger
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
